package com.hvisions.wms.dto.agvScheduling;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "物料分析", description = "物料分析查询DTO")
public class MaterialBoxStatQueryDTO extends  {

    @ApiModelProperty(value = "开始时间", example = "2023-01-01 00:00:00", notes = "查询的起始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2023-12-31 23:59:59", notes = "查询的结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "运输工具编号", example = "TR-2023-001",
            notes = "料框编号，用于精确筛选特定运输工具的物料信息")
    private String transportNo;
}