# 物料料框统计接口使用说明

## 接口地址
`POST /agvScheduling/statistics`

## 功能说明
统计料框使用情况，支持按时间范围和运输工具编号筛选，支持分页和排序功能。

## 请求参数

### MaterialBoxStatQueryDTO 参数说明

#### 分页参数（继承自PageInfo）
- `page`: 页数，默认0（第一页）
- `pageSize`: 每页个数，默认10
- `sort`: 是否排序，默认不排序
- `direction`: 是否正序，默认正序（true为正序ASC，false为倒序DESC）
- `sortCol`: 排序列名，可选值：
  - `palletNo`: 按运输工具编号排序
  - `palletName`: 按运输工具类型排序
  - `useCount`: 按使用次数排序
  - `avgUseDuration`: 按平均使用时长排序

#### 查询条件参数
- `startTime`: 开始时间（格式：yyyy-MM-dd HH:mm:ss）
- `endTime`: 结束时间（格式：yyyy-MM-dd HH:mm:ss）
- `transportNo`: 运输工具编号（可选，用于精确筛选）

## 请求示例

### 示例1：基本分页查询
```json
{
  "page": 0,
  "pageSize": 10,
  "sort": false,
  "startTime": "2023-01-01 00:00:00",
  "endTime": "2023-12-31 23:59:59"
}
```

### 示例2：按使用次数倒序排列
```json
{
  "page": 0,
  "pageSize": 20,
  "sort": true,
  "direction": false,
  "sortCol": "useCount",
  "startTime": "2023-01-01 00:00:00",
  "endTime": "2023-12-31 23:59:59"
}
```

### 示例3：按平均使用时长正序排列
```json
{
  "page": 1,
  "pageSize": 15,
  "sort": true,
  "direction": true,
  "sortCol": "avgUseDuration",
  "startTime": "2023-06-01 00:00:00",
  "endTime": "2023-06-30 23:59:59",
  "transportNo": "AGV-001"
}
```

## 响应结果

返回 `Page<MaterialBoxStatDTO>` 分页对象，包含：

### 分页信息
- `content`: 数据列表
- `totalElements`: 总记录数
- `totalPages`: 总页数
- `size`: 每页大小
- `number`: 当前页码
- `first`: 是否第一页
- `last`: 是否最后一页

### MaterialBoxStatDTO 数据结构
- `palletNo`: 运输工具编号
- `palletName`: 运输工具类型
- `useCount`: 使用次数
- `avgUseDuration`: 平均单次使用时长（分钟）

## 响应示例
```json
{
  "content": [
    {
      "palletNo": "BOX-2023-001",
      "palletName": "A型料框",
      "useCount": 156,
      "avgUseDuration": 360.5
    },
    {
      "palletNo": "BOX-2023-002",
      "palletName": "B型料框",
      "useCount": 142,
      "avgUseDuration": 285.3
    }
  ],
  "totalElements": 50,
  "totalPages": 5,
  "size": 10,
  "number": 0,
  "first": true,
  "last": false
}
```

## 注意事项
1. 只统计已完成的任务（scheduling_state = 3）
2. 时间筛选基于actual_start_time和actual_end_time
3. 平均使用时长计算排除了无效的时间数据
4. 排序功能需要设置sort=true才会生效
5. 默认排序方向为倒序（direction=false）
