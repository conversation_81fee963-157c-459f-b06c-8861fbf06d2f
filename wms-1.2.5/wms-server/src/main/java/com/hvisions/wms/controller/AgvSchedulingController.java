package com.hvisions.wms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.transportTool.TransportToolDTO;
import com.hvisions.thirdparty.common.dto.RCSSendTaskDTO;
import com.hvisions.thirdparty.common.dto.RCSTargetRouteDTO;
import com.hvisions.thirdparty.common.dto.RcsCancelTaskRequestDTO;
import com.hvisions.thirdparty.common.dto.rcs.CancelTaskRequest;
import com.hvisions.thridparty.client.RCSClient;
import com.hvisions.wms.dto.agvScheduling.*;
import com.hvisions.wms.entity.agvScheduling.HvWmAgvScheduling;
import com.hvisions.wms.service.HvWmAgvSchedulingService;
import com.hvisions.wms.utils.SerialCodeUtilsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/hvWmAgvScheduling")
@Api(description = "AGV调度任务管理")
public class AgvSchedulingController {

    @Autowired
    private HvWmAgvSchedulingService agvSchedulingService;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private RCSClient rcsClient;

    @ApiOperation("分页查询")
    @PostMapping("/getPage")
    public Page<HvWmAgvSchedulingShowDTO> getPage(@RequestBody HvWmAgvSchedulingQueryDTO queryDTO){
        return agvSchedulingService.pageList(queryDTO);
    }

    @ApiOperation("根据id查询scheduling")
    @GetMapping("/getSchedulingById/{id}")
    public HvWmAgvSchedulingShowDTO getSchedulingById(@PathVariable Integer id){
        HvWmAgvScheduling one = agvSchedulingService.getOne(new LambdaQueryWrapper<HvWmAgvScheduling>().eq(HvWmAgvScheduling::getId, id));
        return DtoMapper.convert(one, HvWmAgvSchedulingShowDTO.class);
    }

    @ApiOperation("根据TaskNo查询scheduling")
    @GetMapping("/getSchedulingByTaskNo/{taskNo}")
    public HvWmAgvSchedulingShowDTO getSchedulingByTaskNo(@PathVariable String taskNo){
        HvWmAgvScheduling one = agvSchedulingService.getOne( new QueryWrapper<HvWmAgvScheduling>().eq("request_task_no",taskNo));
        return DtoMapper.convert(one, HvWmAgvSchedulingShowDTO.class);
    }

    /**
     * 单纯添加不加下发
     * @param agvSchedulingDTO
     * @param userInfo
     * @return
     */
    @ApiOperation("添加调度")
    @PostMapping("/addScheduling")
    public ResultVO addScheduling(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo){
        if (agvSchedulingService.isExistAgvSchedulingByRequestTaskNo(agvSchedulingDTO.getRequestTaskNo())) {
            return ResultVO.error(500, "请求任务号已存在");
        }
        if (userInfo != null) {
            agvSchedulingDTO.setCreatorName(userInfo.getUserName());
            agvSchedulingDTO.setUpdaterName(userInfo.getUserName());
        }
        agvSchedulingDTO.setCreateTime(LocalDateTime.now());
        agvSchedulingDTO.setUpdateTime(LocalDateTime.now());
        //设置固定编码
        String taskCode = serialCodeUtilsV2.generateCode("AgvTaskNo");
        HvWmAgvScheduling hvWmAgvScheduling = DtoMapper.convert(agvSchedulingDTO, HvWmAgvScheduling.class);
        hvWmAgvScheduling.setTaskNo(taskCode);
        return ResultVO.success(agvSchedulingService.save(hvWmAgvScheduling));
    }

    @ApiOperation("删除调度")
    @DeleteMapping("/delScheduling/{id}")
    public ResultVO delScheduling(@PathVariable Integer id){
        return ResultVO.success(agvSchedulingService.remove(new LambdaQueryWrapper<HvWmAgvScheduling>().eq(HvWmAgvScheduling::getId, id)));
    }

    @ApiOperation("更新调度")
    @PutMapping("/updateScheduling")
    public ResultVO updateScheduling(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO, @UserInfo @ApiIgnore UserInfoDTO userInfo){
        agvSchedulingDTO.setUpdateTime(LocalDateTime.now());
        if (userInfo != null) {
            agvSchedulingDTO.setUpdaterName(userInfo.getUserName());
        }
        HvWmAgvScheduling hvWmAgvScheduling = DtoMapper.convert(agvSchedulingDTO, HvWmAgvScheduling.class);
        return ResultVO.success(agvSchedulingService.update(hvWmAgvScheduling,
                new LambdaQueryWrapper<HvWmAgvScheduling>().eq(HvWmAgvScheduling::getId, hvWmAgvScheduling.getId())));
    }


    /**
     * 前端点击继续可以进行继续任务并且更新agv状态
     * @param agvSchedulingDTO
     * @param userInfo
     * @return
     */
    @ApiOperation("AGV任务下发")
    @PostMapping("/agvTaskDispatch")
    public ResultVO agvTaskDispatch(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO, @ApiIgnore @UserInfo UserInfoDTO userInfo){
        agvSchedulingDTO.setCreateTime(LocalDateTime.now());
        agvSchedulingDTO.setUpdateTime(LocalDateTime.now());
        if (userInfo != null) {
            agvSchedulingDTO.setCreatorName(userInfo.getUserName());
            agvSchedulingDTO.setUpdaterName(userInfo.getUserName());
        }
        agvSchedulingService.updateById(DtoMapper.convert(agvSchedulingDTO,HvWmAgvScheduling.class));
        return agvSchedulingService.agvTaskDispatch(agvSchedulingDTO);
    }

    /**
     * 导出
     *
     * @param hvWmAgvSchedulingQueryDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody HvWmAgvSchedulingQueryDTO hvWmAgvSchedulingQueryDTO) {
        List<HvWmAgvScheduling> list =  agvSchedulingService.findListByCondition(hvWmAgvSchedulingQueryDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(list, HvWmAgvScheduling.class, System.currentTimeMillis() + "AGV调度数据.xlsx"));
    }

    @ApiOperation("AGV任务新增并且下发")
    @PostMapping("/agvTaskAddAndDispatch")
    public ResultVO agvTaskAddAndDispatch(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO, @ApiIgnore @UserInfo UserInfoDTO userInfo){
        if (agvSchedulingService.isExistAgvSchedulingByRequestTaskNo(agvSchedulingDTO.getRequestTaskNo())) {
            return ResultVO.error(500, "请求任务号已存在");
        }
        agvSchedulingDTO.setCreateTime(LocalDateTime.now());
        agvSchedulingDTO.setUpdateTime(LocalDateTime.now());
        if (userInfo != null) {
            agvSchedulingDTO.setCreatorName(userInfo.getUserName());
            agvSchedulingDTO.setUpdaterName(userInfo.getUserName());
        }
        //设置固定编码
        String taskCode = serialCodeUtilsV2.generateCode("AgvTaskNo");
        agvSchedulingDTO.setTaskNo(taskCode);
        HvWmAgvScheduling hvWmAgvScheduling = DtoMapper.convert(agvSchedulingDTO, HvWmAgvScheduling.class);
        boolean save = agvSchedulingService.save(hvWmAgvScheduling);
        if (save) {
            return agvSchedulingService.agvTaskDispatch(agvSchedulingDTO);
        }
        return ResultVO.error(500,"下发失败");

    }

    @ApiOperation("AGV任务取消")
    @PostMapping("/agvTaskCancel")
    public ResultVO agvTaskCancel(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO, @ApiIgnore @UserInfo UserInfoDTO userInfo){
//        修改任务状态
        agvSchedulingDTO.setUpdateTime(LocalDateTime.now());
        agvSchedulingDTO.setUpdaterName(userInfo.getUserName());
        agvSchedulingService.updateById(DtoMapper.convert(agvSchedulingDTO, HvWmAgvScheduling.class));
//        发送agv取消任务请求
        RcsCancelTaskRequestDTO cancelTaskRequest = new RcsCancelTaskRequestDTO();
        if (agvSchedulingDTO.getTaskNo() != null && !agvSchedulingDTO.getTaskNo().isEmpty()) {
            cancelTaskRequest.setRobotTaskCode(agvSchedulingDTO.getTaskNo());
            cancelTaskRequest.setCancelType("DROP");
            return rcsClient.cancelTask(cancelTaskRequest);
        }
        return ResultVO.error(500,"取消任务失败");
    }

    /**
     * 统计料框使用情况，支持按时间范围和运输工具编号筛选
     */
    @ApiOperation("物料料框使用情况的统计")
    @PostMapping("/statistics")
    public List<MaterialBoxStatDTO> getStatistics(MaterialBoxStatQueryDTO queryDTO) {
        return agvSchedulingService.statistics(queryDTO);
    }



}
